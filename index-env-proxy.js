import gplay from "google-play-scraper";

console.log('开始请求 Google Play 数据...');

// 检查环境变量中的代理设置
const httpProxy = process.env.HTTP_PROXY || process.env.http_proxy;
const httpsProxy = process.env.HTTPS_PROXY || process.env.https_proxy;

if (httpProxy || httpsProxy) {
    console.log('检测到代理设置:');
    console.log('HTTP_PROXY:', httpProxy);
    console.log('HTTPS_PROXY:', httpsProxy);
} else {
    console.log('未检测到代理设置');
}

// 请求配置
const requestOptions = {
    category: gplay.category.GAME_ACTION,
    collection: gplay.collection.TOP_FREE,
    num: 5, // 增加到5个应用
    country: 'us',
    lang: 'en'
};

console.log('请求参数:', requestOptions);

async function fetchApps() {
    try {
        console.log('发送请求到 Google Play...');
        const apps = await gplay.list(requestOptions);
        
        console.log('\n✅ 成功获取应用列表!');
        console.log(`获取到 ${apps.length} 个应用:\n`);
        
        apps.forEach((app, index) => {
            console.log(`${index + 1}. ${app.title}`);
            console.log(`   开发者: ${app.developer}`);
            console.log(`   评分: ${app.score || 'N/A'}`);
            console.log(`   价格: ${app.priceText || app.price || 'Free'}`);
            console.log(`   URL: ${app.url}`);
            console.log(`   应用ID: ${app.appId}`);
            console.log('   ---');
        });
        
        return apps;
    } catch (err) {
        console.error('\n❌ 请求失败:');
        console.error('错误信息:', err.message);
        
        // 提供详细的解决方案
        console.log('\n🔧 解决方案:');
        console.log('');
        console.log('1. 使用代理服务器 (推荐):');
        console.log('   在命令行中设置代理环境变量:');
        console.log('   Windows: set HTTPS_PROXY=http://proxy-server:port');
        console.log('   Linux/Mac: export HTTPS_PROXY=http://proxy-server:port');
        console.log('   然后重新运行: node index-env-proxy.js');
        console.log('');
        console.log('2. 使用 VPN:');
        console.log('   - 连接到支持访问 Google 服务的 VPN');
        console.log('   - 确保 VPN 连接稳定后重新运行脚本');
        console.log('');
        console.log('3. 检查网络设置:');
        console.log('   - 确保防火墙允许 Node.js 访问网络');
        console.log('   - 检查是否有企业网络限制');
        console.log('');
        console.log('4. 尝试其他地区:');
        console.log('   - 修改 country 参数 (如: "uk", "ca", "au")');
        console.log('');
        console.log('5. 使用模拟数据进行测试:');
        console.log('   - 运行: node mock-data.js');
        
        throw err;
    }
}

fetchApps().catch(() => {
    console.log('\n如果问题持续存在，请检查网络连接或联系网络管理员。');
    process.exit(1);
});
