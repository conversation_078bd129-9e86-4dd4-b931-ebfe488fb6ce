import gplay from "google-play-scraper";

console.log('开始请求 Google Play 数据...');

// 尝试不同的请求配置
const requestOptions = {
    category: gplay.category.GAME_ACTION,
    collection: gplay.collection.TOP_FREE,
    num: 2,
    // 添加一些请求选项
    country: 'us', // 尝试使用美国地区
    lang: 'en'     // 使用英语
};

console.log('请求参数:', requestOptions);

// 添加重试机制
async function fetchWithRetry(maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            console.log(`尝试第 ${i + 1} 次请求...`);
            const apps = await gplay.list(requestOptions);
            console.log('成功获取应用列表:');
            console.log(`获取到 ${apps.length} 个应用`);
            apps.forEach((app, index) => {
                console.log(`${index + 1}. ${app.title} - ${app.developer}`);
                console.log(`   URL: ${app.url}`);
                console.log(`   评分: ${app.score}`);
                console.log('---');
            });
            return apps;
        } catch (err) {
            console.error(`第 ${i + 1} 次尝试失败:`);
            console.error('错误信息:', err.message);

            if (i === maxRetries - 1) {
                throw err; // 最后一次尝试失败，抛出错误
            }

            // 等待一段时间后重试
            console.log('等待 2 秒后重试...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
}

fetchWithRetry().catch(err => {
    console.error('\n所有重试都失败了:');
    console.error('错误类型:', err.name);
    console.error('错误信息:', err.message);
    console.error('\n可能的解决方案:');
    console.error('1. 检查网络连接');
    console.error('2. 使用 VPN 或代理');
    console.error('3. 检查防火墙设置');
    console.error('4. 尝试不同的地区设置');
});
