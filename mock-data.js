// 模拟 Google Play 数据，用于测试代码逻辑

console.log('使用模拟数据测试...');

// 模拟的应用数据
const mockApps = [
    {
        title: "PUBG MOBILE",
        developer: "PUBG Corporation",
        score: 4.2,
        priceText: "Free",
        price: 0,
        url: "https://play.google.com/store/apps/details?id=com.tencent.ig",
        appId: "com.tencent.ig",
        icon: "https://play-lh.googleusercontent.com/example1.png",
        summary: "The official PUBG designed exclusively for mobile.",
        installs: "500,000,000+",
        genre: "Action",
        genreId: "GAME_ACTION"
    },
    {
        title: "Call of Duty®: Mobile",
        developer: "Activision Publishing, Inc.",
        score: 4.5,
        priceText: "Free",
        price: 0,
        url: "https://play.google.com/store/apps/details?id=com.activision.callofduty.shooter",
        appId: "com.activision.callofduty.shooter",
        icon: "https://play-lh.googleusercontent.com/example2.png",
        summary: "Call of Duty®: Mobile delivers the definitive first-person action experience.",
        installs: "100,000,000+",
        genre: "Action",
        genreId: "GAME_ACTION"
    },
    {
        title: "Garena Free Fire MAX",
        developer: "Garena International I Private Limited",
        score: 4.1,
        priceText: "Free",
        price: 0,
        url: "https://play.google.com/store/apps/details?id=com.dts.freefiremax",
        appId: "com.dts.freefiremax",
        icon: "https://play-lh.googleusercontent.com/example3.png",
        summary: "Free Fire MAX delivers premium gameplay experience in a battle royale.",
        installs: "50,000,000+",
        genre: "Action",
        genreId: "GAME_ACTION"
    },
    {
        title: "Fortnite",
        developer: "Epic Games, Inc.",
        score: 4.0,
        priceText: "Free",
        price: 0,
        url: "https://play.google.com/store/apps/details?id=com.epicgames.fortnite",
        appId: "com.epicgames.fortnite",
        icon: "https://play-lh.googleusercontent.com/example4.png",
        summary: "Fortnite is the completely free multiplayer game.",
        installs: "10,000,000+",
        genre: "Action",
        genreId: "GAME_ACTION"
    },
    {
        title: "Genshin Impact",
        developer: "COGNOSPHERE PTE. LTD.",
        score: 4.6,
        priceText: "Free",
        price: 0,
        url: "https://play.google.com/store/apps/details?id=com.miHoYo.GenshinImpact",
        appId: "com.miHoYo.GenshinImpact",
        icon: "https://play-lh.googleusercontent.com/example5.png",
        summary: "Step into Teyvat, a vast world teeming with life and flowing with elemental energy.",
        installs: "50,000,000+",
        genre: "Action",
        genreId: "GAME_ACTION"
    }
];

// 模拟异步请求
function simulateGooglePlayRequest() {
    return new Promise((resolve) => {
        // 模拟网络延迟
        setTimeout(() => {
            resolve(mockApps);
        }, 1000);
    });
}

async function runMockTest() {
    try {
        console.log('模拟请求 Google Play 数据...');
        console.log('请求参数: { category: "GAME_ACTION", collection: "TOP_FREE", num: 5 }');
        
        const apps = await simulateGooglePlayRequest();
        
        console.log('\n✅ 模拟请求成功!');
        console.log(`获取到 ${apps.length} 个应用:\n`);
        
        apps.forEach((app, index) => {
            console.log(`${index + 1}. ${app.title}`);
            console.log(`   开发者: ${app.developer}`);
            console.log(`   评分: ${app.score}`);
            console.log(`   价格: ${app.priceText}`);
            console.log(`   安装量: ${app.installs}`);
            console.log(`   URL: ${app.url}`);
            console.log(`   应用ID: ${app.appId}`);
            console.log('   ---');
        });
        
        console.log('\n📊 统计信息:');
        const avgScore = apps.reduce((sum, app) => sum + app.score, 0) / apps.length;
        console.log(`平均评分: ${avgScore.toFixed(2)}`);
        
        const freeApps = apps.filter(app => app.price === 0).length;
        console.log(`免费应用: ${freeApps}/${apps.length}`);
        
        console.log('\n✨ 模拟测试完成! 这展示了当网络连接正常时您会得到的数据格式。');
        
    } catch (err) {
        console.error('模拟测试失败:', err);
    }
}

runMockTest();
