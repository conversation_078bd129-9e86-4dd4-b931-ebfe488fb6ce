# Google Play Scraper 使用指南

## 问题描述

您在使用 `google-play-scraper` 库时遇到了网络连接超时的问题：
```
Error: Error requesting Google Play:connect ETIMEDOUT 142.250.69.174:443
```

## 问题原因

通过网络连接测试发现，您的网络环境无法直接访问 Google 的服务（包括 Google Play），这是导致连接失败的主要原因。

## 解决方案

### 1. 使用 VPN（推荐）
- 连接到支持访问 Google 服务的 VPN 服务器
- 确保 VPN 连接稳定后重新运行脚本

### 2. 使用代理服务器
```bash
# Windows
set HTTPS_PROXY=http://proxy-server:port
set HTTP_PROXY=http://proxy-server:port

# Linux/Mac
export HTTPS_PROXY=http://proxy-server:port
export HTTP_PROXY=http://proxy-server:port

# 然后运行
node index-env-proxy.js
```

### 3. 修改代理配置文件
编辑 `index-with-proxy.js` 中的 `PROXY_CONFIG` 设置：
```javascript
const PROXY_CONFIG = {
    host: 'your-proxy-host.com',
    port: 8080,
    username: 'your-username', // 可选
    password: 'your-password'  // 可选
};
```

## 文件说明

### 主要文件
- `index.js` - 原始代码，已添加重试机制和错误处理
- `index-env-proxy.js` - 支持环境变量代理配置的版本
- `index-with-proxy.js` - 支持手动代理配置的版本
- `mock-data.js` - 模拟数据测试，用于验证代码逻辑
- `test-connection.js` - 网络连接测试工具

### 测试文件
- `mock-data.js` - 运行此文件可以看到正常情况下的数据格式

## 使用步骤

### 方法一：使用 VPN
1. 连接 VPN
2. 运行 `node index.js`

### 方法二：使用环境变量代理
1. 设置代理环境变量
2. 运行 `node index-env-proxy.js`

### 方法三：测试代码逻辑
1. 运行 `node mock-data.js` 查看预期的数据格式

## 代码改进

已对原始代码进行了以下改进：
1. ✅ 添加了 `"type": "module"` 到 package.json
2. ✅ 添加了重试机制（最多3次）
3. ✅ 添加了详细的错误处理和日志
4. ✅ 添加了代理支持
5. ✅ 添加了模拟数据用于测试

## API 使用示例

```javascript
import gplay from "google-play-scraper";

// 基本用法
const apps = await gplay.list({
    category: gplay.category.GAME_ACTION,
    collection: gplay.collection.TOP_FREE,
    num: 5,
    country: 'us',
    lang: 'en'
});

// 获取单个应用详情
const appDetails = await gplay.app({
    appId: 'com.tencent.ig'
});

// 搜索应用
const searchResults = await gplay.search({
    term: 'PUBG',
    num: 10
});
```

## 常见问题

### Q: 为什么连接超时？
A: 您的网络环境可能无法直接访问 Google 服务，需要使用 VPN 或代理。

### Q: 如何验证代码逻辑？
A: 运行 `node mock-data.js` 查看模拟数据输出。

### Q: 如何测试网络连接？
A: 运行 `node test-connection.js` 测试到各个服务器的连接。

## 技术支持

如果问题持续存在，请：
1. 检查网络连接
2. 确认代理设置正确
3. 尝试不同的 VPN 服务器
4. 联系网络管理员了解网络限制
