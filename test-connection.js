import https from 'https';
import http from 'http';

console.log('测试网络连接...');

// 测试基本的 HTTPS 连接
function testConnection(hostname, port = 443) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: hostname,
            port: port,
            method: 'GET',
            timeout: 10000
        };

        const req = https.request(options, (res) => {
            console.log(`✓ 成功连接到 ${hostname}:${port} - 状态码: ${res.statusCode}`);
            resolve(true);
        });

        req.on('error', (err) => {
            console.log(`✗ 连接失败 ${hostname}:${port} - ${err.message}`);
            reject(err);
        });

        req.on('timeout', () => {
            console.log(`✗ 连接超时 ${hostname}:${port}`);
            req.destroy();
            reject(new Error('Connection timeout'));
        });

        req.end();
    });
}

async function runTests() {
    const testHosts = [
        'www.google.com',
        'play.google.com',
        'www.baidu.com',
        'www.github.com'
    ];

    console.log('开始测试连接到各个服务器...\n');

    for (const host of testHosts) {
        try {
            await testConnection(host);
        } catch (err) {
            // 错误已经在 testConnection 中打印了
        }
        // 等待一秒再测试下一个
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n测试完成。');
    console.log('\n如果 Google 相关的服务连接失败，可能需要:');
    console.log('1. 使用 VPN 连接');
    console.log('2. 配置代理服务器');
    console.log('3. 使用其他网络环境');
}

runTests().catch(console.error);
