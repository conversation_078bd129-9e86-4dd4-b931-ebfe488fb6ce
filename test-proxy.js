import https from 'https';
import { HttpsProxyAgent } from 'https-proxy-agent';

console.log('测试代理连接...');

// 您的代理配置
const proxyUrl = 'http://127.0.0.1:7899';
console.log('代理地址:', proxyUrl);

// 创建代理 agent
const agent = new HttpsProxyAgent(proxyUrl);

// 测试连接到 Google
function testProxyConnection() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'www.google.com',
            port: 443,
            path: '/',
            method: 'GET',
            agent: agent,
            timeout: 10000
        };

        console.log('尝试通过代理连接到 Google...');
        
        const req = https.request(options, (res) => {
            console.log('✅ 代理连接成功!');
            console.log('状态码:', res.statusCode);
            console.log('响应头:', res.headers);
            resolve(true);
        });

        req.on('error', (err) => {
            console.log('❌ 代理连接失败:', err.message);
            reject(err);
        });

        req.on('timeout', () => {
            console.log('❌ 代理连接超时');
            req.destroy();
            reject(new Error('Proxy connection timeout'));
        });

        req.end();
    });
}

// 测试直接连接（不使用代理）
function testDirectConnection() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'www.google.com',
            port: 443,
            path: '/',
            method: 'GET',
            timeout: 10000
        };

        console.log('尝试直接连接到 Google（不使用代理）...');
        
        const req = https.request(options, (res) => {
            console.log('✅ 直接连接成功!');
            console.log('状态码:', res.statusCode);
            resolve(true);
        });

        req.on('error', (err) => {
            console.log('❌ 直接连接失败:', err.message);
            reject(err);
        });

        req.on('timeout', () => {
            console.log('❌ 直接连接超时');
            req.destroy();
            reject(new Error('Direct connection timeout'));
        });

        req.end();
    });
}

async function runTests() {
    console.log('=== 开始网络连接测试 ===\n');
    
    // 测试1: 直接连接
    console.log('测试1: 直接连接');
    try {
        await testDirectConnection();
    } catch (err) {
        console.log('直接连接失败，这是预期的结果');
    }
    
    console.log('\n---\n');
    
    // 测试2: 代理连接
    console.log('测试2: 代理连接');
    try {
        await testProxyConnection();
        console.log('\n🎉 代理工作正常！现在可以尝试运行 Google Play scraper');
    } catch (err) {
        console.log('\n❌ 代理连接失败。请检查:');
        console.log('1. 代理服务器是否在 127.0.0.1:7899 上运行');
        console.log('2. 代理服务器是否需要认证');
        console.log('3. 防火墙是否阻止了连接');
    }
    
    console.log('\n=== 测试完成 ===');
}

runTests().catch(console.error);
